models:
  - name: stg_locations
    description: List of open locations with basic cleaning and transformation applied, one row per location (Version 1).
    columns:
      - name: location_id
        description: The unique key for each location.
        data_tests:
          - not_null
          - unique
      - name: location_name
        description: The name of the location/store.
        data_tests:
          - not_null
      - name: tax_rate
        description: The tax rate applied at this location.
        data_tests:
          - not_null
          - dbt_utils.accepted_range:
              min_value: 0
              max_value: 1
      - name: opened_date
        description: The date when the location opened (truncated to day).
        data_tests:
          - not_null

  - name: stg_locations_v2
    description: Enhanced list of open locations with descriptive details (Version 2).
    columns:
      - name: location_id
        description: The unique key for each location.
        data_tests:
          - not_null
          - unique
      - name: location_name
        description: The name of the location/store.
        data_tests:
          - not_null
      - name: tax_rate
        description: The tax rate applied at this location.
        data_tests:
          - not_null
          - dbt_utils.accepted_range:
              min_value: 0
              max_value: 1
      - name: opened_date
        description: The date when the location opened (truncated to day).
        data_tests:
          - not_null
      - name: location_description
        description: A descriptive text about the location and its characteristics.
        data_tests:
          - not_null

unit_tests:
  - name: test_does_location_opened_at_trunc_to_date_v1
    description: "Check that opened_at timestamp is properly truncated to a date in v1."
    model: stg_locations
    given:
      - input: source('ecom', 'raw_stores')
        rows:
          - {
              id: 1,
              name: "Vice City",
              tax_rate: 0.2,
              opened_at: "2016-09-01T00:00:00",
            }
          - {
              id: 2,
              name: "San Andreas",
              tax_rate: 0.1,
              opened_at: "2079-10-27T23:59:59.9999",
            }
    expect:
      rows:
        - {
            location_id: 1,
            location_name: "Vice City",
            tax_rate: 0.2,
            opened_date: "2016-09-01",
          }
        - {
            location_id: 2,
            location_name: "San Andreas",
            tax_rate: 0.1,
            opened_date: "2079-10-27",
          }

  - name: test_does_location_opened_at_trunc_to_date_v2
    description: "Check that opened_at timestamp is properly truncated to a date and description is added in v2."
    model: stg_locations_v2
    given:
      - input: source('ecom', 'raw_stores')
        rows:
          - {
              id: 1,
              name: "Vice City",
              tax_rate: 0.2,
              opened_at: "2016-09-01T00:00:00",
            }
          - {
              id: 2,
              name: "San Andreas",
              tax_rate: 0.1,
              opened_at: "2079-10-27T23:59:59.9999",
            }
    expect:
      rows:
        - {
            location_id: 1,
            location_name: "Vice City",
            tax_rate: 0.2,
            opened_date: "2016-09-01",
            location_description: "Store location with unique local character and community",
          }
        - {
            location_id: 2,
            location_name: "San Andreas",
            tax_rate: 0.1,
            opened_date: "2079-10-27",
            location_description: "Store location with unique local character and community",
          }
