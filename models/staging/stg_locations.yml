models:
  - name: stg_locations
    description: List of open locations with basic cleaning and transformation applied, one row per location.
    columns:
      - name: location_id
        description: The unique key for each location.
        data_tests:
          - not_null
          - unique

unit_tests:
  - name: test_does_location_opened_at_trunc_to_date
    description: "Check that opened_at timestamp is properly truncated to a date."
    model: stg_locations
    given:
      - input: source('ecom', 'raw_stores')
        rows:
          - {
              id: 1,
              name: "Vice City",
              tax_rate: 0.2,
              opened_at: "2016-09-01T00:00:00",
            }
          - {
              id: 2,
              name: "San Andreas",
              tax_rate: 0.1,
              opened_at: "2079-10-27T23:59:59.9999",
            }
    expect:
      rows:
        - {
            location_id: 1,
            location_name: "Vice City",
            tax_rate: 0.2,
            opened_date: "2016-09-01",
          }
        - {
            location_id: 2,
            location_name: "San Andreas",
            tax_rate: 0.1,
            opened_date: "2079-10-27",
          }
