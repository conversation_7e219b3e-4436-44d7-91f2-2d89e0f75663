{{
  config(
    materialized='view'
  )
}}

with

source as (

    select * from {{ source('ecom', 'raw_stores') }}

),

renamed as (

    select

        ----------  ids
        id as location_id,

        ---------- text
        name as location_name,

        ---------- numerics
        tax_rate,

        ---------- timestamps
        {{ dbt.date_trunc('day', 'opened_at') }} as opened_date

    from source

),

with_description as (

    select 
        location_id,
        location_name,
        tax_rate,
        opened_date,
        
        ---------- generated description column
        case 
            when location_name = 'Philadelphia' then 'Historic city in Pennsylvania, known for its rich American history'
            when location_name = 'Brooklyn' then 'Vibrant borough of New York City with diverse neighborhoods'
            when location_name = 'Chicago' then 'Major city in Illinois, famous for architecture and deep-dish pizza'
            when location_name = 'San Francisco' then 'Tech hub in California with iconic Golden Gate Bridge'
            when location_name = 'New Orleans' then 'Cultural melting pot in Louisiana, famous for jazz and cuisine'
            when location_name = 'Los Angeles' then 'Entertainment capital of California, home to Hollywood'
            else 'Store location with unique local character and community'
        end as location_description
        
    from renamed

)

select * from with_description
