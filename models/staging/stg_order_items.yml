models:
  - name: stg_order_items
    description: Individual food and drink items that make up our orders, one row per item.
    columns:
      - name: order_item_id
        description: The unique key for each order item.
        data_tests:
          - not_null
          - unique
      - name: order_id
        description: The corresponding order each order item belongs to
        data_tests:
          - not_null
          - relationships:
              to: ref('stg_orders')
              field: order_id
