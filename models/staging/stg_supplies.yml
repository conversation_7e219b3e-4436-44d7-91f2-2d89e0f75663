models:
  - name: stg_supplies
    description: >
      List of our supply expenses data with basic cleaning and transformation applied.

      One row per supply cost, not per supply. As supply costs fluctuate they receive a new row with a new UUID. Thus there can be multiple rows per supply_id.
    columns:
      - name: supply_uuid
        description: The unique key of our supplies per cost.
        data_tests:
          - not_null
          - unique
