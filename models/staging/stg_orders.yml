models:
  - name: stg_orders
    description: Order data with basic cleaning and transformation applied, one row per order.
    data_tests:
      - dbt_utils.expression_is_true:
          expression: "order_total - tax_paid = subtotal"
    columns:
      - name: order_id
        description: The unique key for each order.
        data_tests:
          - not_null
          - unique

      - name: location_id
        description: The store/location where the order was placed.
        data_tests:
          - not_null

      - name: customer_id
        description: The customer who placed the order.
        data_tests:
          - not_null

      - name: subtotal_cents
        description: Order subtotal in cents (before tax).
        data_tests:
          - not_null
          - dbt_utils.accepted_range:
              min_value: 0

      - name: tax_paid_cents
        description: Tax amount paid in cents.
        data_tests:
          - not_null
          - dbt_utils.accepted_range:
              min_value: 0

      - name: order_total_cents
        description: Total order amount in cents (subtotal + tax).
        data_tests:
          - not_null
          - dbt_utils.accepted_range:
              min_value: 0

      - name: subtotal
        description: Order subtotal in dollars (before tax).
        data_tests:
          - not_null
          - dbt_utils.accepted_range:
              min_value: 0

      - name: tax_paid
        description: Tax amount paid in dollars.
        data_tests:
          - not_null
          - dbt_utils.accepted_range:
              min_value: 0

      - name: order_total
        description: Total order amount in dollars (subtotal + tax).
        data_tests:
          - not_null
          - dbt_utils.accepted_range:
              min_value: 0

      - name: ordered_at
        description: Date when the order was placed (truncated to day).
        data_tests:
          - not_null
