semantic_models:
  #The name of the semantic model.
  - name: products
    description: |
      Product dimension table. The grain of the table is one row per product.
    #The name of the dbt model and schema
    model: ref('products')
    #Entities. These usually corespond to keys in the table.
    entities:
      - name: product
        type: primary
        expr: product_id
    #Dimensions. Either categorical or time. These add additonal context to metrics. The typical querying pattern is Metric by Dimension.
    dimensions:
      - name: product_name
        type: categorical
      - name: product_type
        type: categorical
      - name: product_description
        type: categorical
      - name: is_food_item
        type: categorical
      - name: is_drink_item
        type: categorical
      - name: product_price
        type: categorical
