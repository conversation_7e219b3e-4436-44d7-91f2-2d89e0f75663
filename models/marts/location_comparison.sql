{{
  config(
    materialized='view'
  )
}}

-- This model demonstrates how to use both versions of stg_locations
-- It shows the difference between v1 (basic) and v2 (with descriptions)

with

locations_v1 as (
    select 
        location_id,
        location_name,
        tax_rate,
        opened_date,
        'v1' as version
    from {{ ref('stg_locations') }}
),

locations_v2 as (
    select 
        location_id,
        location_name,
        tax_rate,
        opened_date,
        'v2' as version,
        location_description
    from {{ ref('stg_locations_v2') }}
),

-- Union both versions to show the difference
combined as (
    select 
        location_id,
        location_name,
        tax_rate,
        opened_date,
        version,
        null as location_description
    from locations_v1
    
    union all
    
    select 
        location_id,
        location_name,
        tax_rate,
        opened_date,
        version,
        location_description
    from locations_v2
)

select * from combined
order by location_name, version
