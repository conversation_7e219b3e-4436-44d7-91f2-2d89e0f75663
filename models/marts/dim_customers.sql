{{
    config(
        materialized='table',
        unique_key='customer_sk'
    )
}}

with

-- Source data
customers as (
    select * from {{ ref('stg_customers') }}
),

customer_addresses as (
    select * from {{ ref('stg_customer_addresses') }}
),

-- Get the current date for setting valid_to dates
current_date_value as (
    select cast(current_timestamp as date) as today
),

-- Create a list of all customer_id and effective_date combinations
customer_dates as (
    select
        customer_id,
        effective_date
    from customer_addresses
),

-- Create a window function to determine valid_to dates
customer_address_periods as (
    select
        customer_addresses.customer_id,
        customer_addresses.address,
        customer_addresses.city,
        customer_addresses.state,
        customer_addresses.zip_code,
        customer_addresses.effective_date as valid_from,
        coalesce(
            lead(customer_addresses.effective_date) over (
                partition by customer_addresses.customer_id
                order by customer_addresses.effective_date
            ),
            (select today from current_date_value)
        ) as valid_to
    from customer_addresses
),

-- Join with customer information
customer_with_surrogate_key as (
    select
        -- Create a surrogate key for the dimension
        {{ dbt_utils.generate_surrogate_key(['customers.customer_id', 'cap.valid_from']) }} as customer_sk,
        customers.customer_id,
        customers.customer_name,
        cap.address,
        cap.city,
        cap.state,
        cap.zip_code,
        cap.valid_from,
        cap.valid_to,
        -- Is this the current record?
        case
            when cap.valid_to = (select today from current_date_value)
            then true
            else false
        end as is_current,
        -- Version number for the record
        row_number() over (
            partition by customers.customer_id
            order by cap.valid_from
        ) as version
    from customers
    inner join customer_address_periods as cap
        on customers.customer_id = cap.customer_id
)

select * from customer_with_surrogate_key
