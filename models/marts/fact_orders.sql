{{
    config(
        materialized='table'
    )
}}

with

orders as (
    select * from {{ ref('stg_orders') }}
),

order_items as (
    select * from {{ ref('order_items') }}
),

-- Get the current customer dimension with surrogate keys
dim_customers as (
    select * from {{ ref('dim_customers') }}
),

-- Aggregate order items to order level
order_items_summary as (
    select
        order_id,
        sum(product_price) as items_total
    from order_items
    group by 1
),

-- Join orders with customer dimension using point-in-time lookup
orders_with_customer_sk as (
    select
        orders.order_id,
        orders.ordered_at,
        orders.order_total,
        orders.tax_paid,
        orders.subtotal,

        -- Find the correct customer version at the time of the order
        dim_customers.customer_sk,
        dim_customers.customer_id,
        dim_customers.customer_name,
        dim_customers.address,
        dim_customers.city,
        dim_customers.state,
        dim_customers.zip_code,
        dim_customers.version as customer_version_at_order_time

    from orders

    -- Point-in-time join to get the customer record that was active when the order was placed
    -- Using inner join to ensure we only get orders with matching customer records
    inner join dim_customers
        on orders.customer_id = dim_customers.customer_id
        and orders.ordered_at >= dim_customers.valid_from
        and orders.ordered_at < dim_customers.valid_to
),

-- Final fact table
final as (
    select
        orders_with_customer_sk.order_id,
        orders_with_customer_sk.ordered_at,
        orders_with_customer_sk.customer_sk,
        orders_with_customer_sk.customer_id,
        orders_with_customer_sk.order_total,
        orders_with_customer_sk.tax_paid,
        orders_with_customer_sk.subtotal,
        order_items_summary.items_total,

        -- Add customer information at time of order
        orders_with_customer_sk.customer_name,
        orders_with_customer_sk.address as delivery_address,
        orders_with_customer_sk.city as delivery_city,
        orders_with_customer_sk.state as delivery_state,
        orders_with_customer_sk.zip_code as delivery_zip_code,
        orders_with_customer_sk.customer_version_at_order_time

    from orders_with_customer_sk
    left join order_items_summary
        on orders_with_customer_sk.order_id = order_items_summary.order_id
)

select * from final
