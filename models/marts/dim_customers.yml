version: 2

models:
  - name: dim_customers
    description: SCD Type 2 dimension table for customers with address history
    columns:
      - name: customer_sk
        description: Surrogate key for the customer dimension
        tests:
          - unique
          - not_null
      
      - name: customer_id
        description: Natural key for the customer
        tests:
          - not_null
      
      - name: valid_from
        description: Date from which this customer record is valid
        tests:
          - not_null
      
      - name: valid_to
        description: Date until which this customer record is valid
        tests:
          - not_null
      
      - name: is_current
        description: Flag indicating if this is the current version of the customer record
        tests:
          - not_null
      
      - name: version
        description: Version number of the customer record
        tests:
          - not_null

  - name: fact_orders
    description: Fact table for orders with point-in-time customer information
    columns:
      - name: order_id
        description: Unique identifier for the order
        tests:
          - unique
          - not_null
      
      - name: customer_sk
        description: Surrogate key linking to the customer dimension
        tests:
          - not_null
          - relationships:
              to: ref('dim_customers')
              field: customer_sk
      
      - name: ordered_at
        description: Timestamp when the order was placed
        tests:
          - not_null
