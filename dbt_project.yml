config-version: 2

name: "jaffle_shop"
version: "3.0.0"
require-dbt-version: ">=1.5.0"

# Not using dbt cloud:
# dbt-cloud:
#   project-id: 275557 # Put your project id here

profile: jaffle_shop # Using the profile defined in profiles.yml

model-paths: ["models"]
analysis-paths: ["analyses"]
test-paths: ["data-tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]

target-path: "target"
clean-targets:
  - "target"
  - "dbt_packages"

vars:
  "dbt_date:time_zone": "America/Los_Angeles"

seeds:
  jaffle_shop:
    +schema: raw
    customer_addresses:
      +schema: raw

models:
  jaffle_shop:
    staging:
      +materialized: view
    marts:
      +materialized: table
